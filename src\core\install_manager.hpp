#pragma once

#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>
#include "../utils/output.hpp"
#include "config.hpp"
#include "manifest.hpp"
#include "dependency_resolver.hpp"
#include "../utils/download_manager.hpp"
#include "../utils/extractor.hpp"
#include "../utils/shim_manager.hpp"

namespace sco {

struct InstallationInfo {
    std::string app_name;
    std::string version;
    std::string bucket;
    std::filesystem::path install_path;
    std::chrono::system_clock::time_point install_time;
    std::vector<std::string> installed_files;
    std::vector<std::string> created_shims;
};

class InstallManager {
public:
    struct InstallOptions {
        bool global = false;
        bool skip_dependencies = false;
        bool force_reinstall = false;
        bool no_cache = false;
        std::string architecture = "64bit";
    };
    
    struct InstallResult {
        bool success = false;
        std::string error_message;
        std::vector<std::string> installed_apps;
        std::vector<std::string> failed_apps;
        std::chrono::milliseconds total_duration{0};
    };
    
    static InstallResult install_apps(const std::vector<std::string>& app_names, 
                                    const InstallOptions& options = {}) {
        InstallManager manager(options);
        return manager.perform_installation(app_names);
    }
    
private:
    InstallOptions options_;
    Config& config_;
    
    explicit InstallManager(const InstallOptions& options) 
        : options_(options), config_(Config::instance()) {
        config_.load();
        if (options_.global) {
            config_.set_global_mode(true);
        }
    }
    
    InstallResult perform_installation(const std::vector<std::string>& app_names) {
        InstallResult result;
        auto start_time = std::chrono::steady_clock::now();

        output::info("Starting installation of {} app(s)", app_names.size());

        try {
            // Ensure 7zip is available for extraction
            if (!ensure_7zip_available()) {
                result.error_message = "Failed to ensure 7zip is available for extraction";
                return result;
            }
            // Step 1: Resolve dependencies
            std::vector<std::string> install_order;
            if (!options_.skip_dependencies) {
                auto resolve_result = DependencyResolver::resolve(app_names);
                if (!resolve_result.success) {
                    result.error_message = "Dependency resolution failed";
                    if (!resolve_result.circular_dependencies.empty()) {
                        result.error_message += ": Circular dependencies detected";
                    }
                    if (!resolve_result.missing_dependencies.empty()) {
                        result.error_message += ": Missing dependencies";
                    }
                    return result;
                }
                install_order = resolve_result.install_order;
            } else {
                install_order = app_names;
            }
            
            // Step 2: Filter out already installed apps (unless force reinstall)
            if (!options_.force_reinstall) {
                install_order = DependencyResolver::filter_installed_apps(install_order);
            }
            
            if (install_order.empty()) {
                output::info("All requested apps are already installed");
                result.success = true;
                return result;
            }

            output::info("Will install {} app(s) in order: {}", install_order.size(),
                       join_strings(install_order, ", "));
            
            // Step 3: Install each app in order
            for (const auto& app_name : install_order) {
                if (install_single_app(app_name)) {
                    result.installed_apps.push_back(app_name);
                    output::info("Successfully installed: {}", app_name);
                } else {
                    result.failed_apps.push_back(app_name);
                    output::error("Failed to install: {}", app_name);
                    
                    // Decide whether to continue or abort
                    if (std::find(app_names.begin(), app_names.end(), app_name) != app_names.end()) {
                        // This was a requested app, not a dependency - abort
                        result.error_message = "Failed to install requested app: " + app_name;
                        break;
                    }
                    // This was a dependency - continue but log the failure
                }
            }
            
            result.success = result.failed_apps.empty() || 
                           (!result.installed_apps.empty() && result.failed_apps.size() < install_order.size());
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            output::error("Installation failed with exception: {}", e.what());
        }
        
        auto end_time = std::chrono::steady_clock::now();
        result.total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        output::info("Installation completed in {}ms. Success: {}, Installed: {}, Failed: {}",
                   result.total_duration.count(), result.success,
                   result.installed_apps.size(), result.failed_apps.size());
        
        return result;
    }
    
    bool install_single_app(const std::string& app_name) {
        try {
            // Step 1: Find and parse manifest
            auto manifest = ManifestParser::find_and_parse(app_name);
            if (!manifest.is_valid()) {
                output::error("Couldn't find manifest for '{}'", app_name);
                return false;
            }

            // Show Scoop-style installation header
            std::cout << "Installing '" << manifest.name << "' (" << manifest.version << ") ["
                      << options_.architecture << "]";
            if (!manifest.bucket.empty()) {
                std::cout << " from '" << manifest.bucket << "' bucket";
            }
            std::cout << "\n";

            // Step 2: Prepare installation directory (version-specific)
            auto version_dir = prepare_version_directory(manifest);
            if (version_dir.empty()) {
                output::error("Failed to prepare app directory for: {}", app_name);
                return false;
            }

            // Keep reference to original directory before linking
            auto original_dir = version_dir;

            // Step 3: Download files
            auto urls = manifest.get_urls(options_.architecture);
            if (urls.empty()) {
                output::error("No download URLs found for: {}", app_name);
                return false;
            }

            std::vector<std::filesystem::path> downloaded_files;
            for (const auto& url_info : urls) {
                auto downloaded_file = download_file(url_info, version_dir, manifest.name, manifest.version);
                if (downloaded_file.empty()) {
                    output::error("Failed to download file for: {}", app_name);
                    return false;
                }
                downloaded_files.push_back(downloaded_file);
            }

            // Step 4: Extract files
            for (const auto& downloaded_file : downloaded_files) {
                if (!extract_file(downloaded_file, version_dir, manifest)) {
                    output::error("Failed to extract file for: {}", app_name);
                    return false;
                }
            }

            // Step 5: Run pre-install script
            auto pre_install_script = manifest.get_pre_install(options_.architecture);
            if (!pre_install_script.empty()) {
                if (!run_script(pre_install_script, version_dir, "pre-install")) {
                    output::warn("Pre-install script failed for: {}", app_name);
                    // Don't fail installation for script failures
                }
            }

            // Step 6: Run installer if present
            run_installer(manifest, version_dir, false); // false = install mode

            // Step 7: Ensure install directory is not in PATH
            ensure_install_dir_not_in_path(version_dir);

            // Step 8: Link current directory (Scoop's link_current)
            auto current_dir = link_current_directory(version_dir);

            // Step 9: Create shims (using current directory)
            auto bin_entries = manifest.get_bin(options_.architecture);
            if (!bin_entries.empty()) {
                if (!ShimManager::create_shims_for_app(app_name, current_dir, bin_entries)) {
                    output::warn("Failed to create some shims for: {}", app_name);
                    // Don't fail installation for shim creation failures
                }
            }

            // Step 10: Create start menu shortcuts
            create_startmenu_shortcuts(manifest, current_dir);

            // Step 11: Install PowerShell modules
            install_psmodule(manifest, current_dir);

            // Step 12: Add to PATH environment variable
            env_add_path(manifest, current_dir);

            // Step 13: Set environment variables
            env_set(manifest);

            // Step 14: Handle persist directories
            handle_persist_directories(manifest, original_dir);

            // Step 15: Set persist permissions
            persist_permission(manifest);

            // Step 16: Run post-install script
            auto post_install_script = manifest.get_post_install(options_.architecture);
            if (!post_install_script.empty()) {
                if (!run_script(post_install_script, version_dir, "post-install")) {
                    output::warn("Post-install script failed for: {}", app_name);
                    // Don't fail installation for script failures
                }
            }

            // Step 17: Save installation info (Scoop's save_installed_manifest and save_install_info)
            if (!save_installation_info(manifest, version_dir)) {
                output::warn("Failed to save installation info for: {}", app_name);
                // Don't fail installation for this
            }

            // Step 18: Show success message
            std::cout << "'" << manifest.name << "' (" << manifest.version << ") was installed successfully!\n";

            // Step 19: Show notes if any
            show_app_notes(manifest, current_dir, original_dir);
            
            return true;
            
        } catch (const std::exception& e) {
            output::error("Exception during installation of {}: {}", app_name, e.what());
            return false;
        }
    }
    
    std::filesystem::path prepare_version_directory(const Manifest& manifest) {
        auto apps_dir = options_.global ? config_.get_global_apps_dir() : config_.get_apps_dir();
        auto app_dir = apps_dir / manifest.name;
        auto version_dir = app_dir / manifest.version;

        try {
            // Create version-specific directory (Scoop's ensure versiondir)
            std::filesystem::create_directories(version_dir);

            return version_dir;

        } catch (const std::exception& e) {
            output::error("Failed to prepare version directory: {}", e.what());
            return {};
        }
    }

    std::filesystem::path link_current_directory(const std::filesystem::path& version_dir) {
        // Scoop's link_current function
        auto app_dir = version_dir.parent_path();
        auto current_dir = app_dir / "current";

        try {
            // Remove existing "current" symlink/directory
            if (std::filesystem::exists(current_dir)) {
                std::filesystem::remove_all(current_dir);
            }

            // Create "current" junction/symlink to version directory
#ifdef _WIN32
            // On Windows, create a junction point (like Scoop)
            std::string command = "mklink /J \"" + current_dir.string() + "\" \"" + version_dir.string() + "\"";
            int result = system(command.c_str());
            if (result != 0) {
                output::warn("Failed to create junction, using directory copy instead");
                std::filesystem::create_directories(current_dir);
                return version_dir; // Return version dir if junction fails
            }
#else
            // On Unix-like systems, create a symbolic link
            std::filesystem::create_symlink(version_dir, current_dir);
#endif

            return current_dir;

        } catch (const std::exception& e) {
            output::warn("Failed to create current directory link: {}", e.what());
            return version_dir; // Return version dir as fallback
        }
    }
    
    std::filesystem::path download_file(const ManifestUrl& url_info,
                                      const std::filesystem::path& app_dir,
                                      const std::string& app_name,
                                      const std::string& version) {
        auto cache_dir = config_.get_cache_dir();
        std::filesystem::create_directories(cache_dir);

        // Generate Scoop-compatible filename: app#version#hash.ext
        std::string filename = DownloadManager::generate_cache_filename(
            app_name, version, url_info.url, url_info.hash);

        auto cache_file = cache_dir / filename;
        
        // Check if file already exists in cache and hash matches
        if (std::filesystem::exists(cache_file) && !options_.no_cache) {
            if (DownloadManager::verify_hash(cache_file, url_info.hash)) {
                std::cout << "Loading " << filename << " from cache.\n";
                return cache_file;
            } else {
                output::warn("Cached file hash mismatch, re-downloading");
                std::filesystem::remove(cache_file);
            }
        }
        auto result = DownloadManager::download_with_progress(url_info.url, cache_file);

        if (!result.success) {
            output::error("Download failed: {}", result.error_message);
            return {};
        }

        // Verify hash
        if (!DownloadManager::verify_hash(cache_file, url_info.hash)) {
            output::error("Downloaded file hash verification failed");
            std::filesystem::remove(cache_file);
            return {};
        }

        std::cout << "Checking hash of " << filename << " ... ok.\n";
        
        return cache_file;
    }
    
    bool extract_file(const std::filesystem::path& archive_path,
                     const std::filesystem::path& app_dir,
                     const Manifest& manifest) {
        
        if (!Extractor::is_extractable(archive_path)) {
            // File is not an archive, just copy it
            try {
                auto dest_file = app_dir / archive_path.filename();
                std::filesystem::copy_file(archive_path, dest_file);
                return true;
            } catch (const std::exception& e) {
                output::error("Failed to copy file: {}", e.what());
                return false;
            }
        }
        
        auto extract_dir = manifest.get_architecture(options_.architecture) ? 
                          manifest.get_architecture(options_.architecture)->extract_dir : 
                          manifest.extract_dir;
        auto extract_to = manifest.get_architecture(options_.architecture) ? 
                         manifest.get_architecture(options_.architecture)->extract_to : 
                         manifest.extract_to;
        
        std::cout << "Extracting " << archive_path.filename().string() << " ... ";
        auto result = Extractor::extract_archive(archive_path, app_dir, extract_dir, extract_to);

        if (!result.success) {
            std::cout << "failed.\n";
            output::error("Extraction failed: {}", result.error_message);
            return false;
        }

        std::cout << "done.\n";
        
        return true;
    }
    
    bool run_script(const std::string& script, 
                   const std::filesystem::path& app_dir,
                   const std::string& script_type) {
        output::info("Running {} script", script_type);
        
        try {
            // Create temporary script file
            auto temp_script = app_dir / ("temp_" + script_type + ".ps1");
            
            std::ofstream script_file(temp_script);
            if (!script_file.is_open()) {
                output::error("Failed to create temporary script file");
                return false;
            }
            
            script_file << script;
            script_file.close();
            
            // Execute script
            std::string command = "powershell.exe -ExecutionPolicy Bypass -File \"" + 
                                temp_script.string() + "\"";
            
            output::debug("Executing script: {}", command);

            int exit_code = system(command.c_str());

            // Clean up temporary script
            std::filesystem::remove(temp_script);

            if (exit_code == 0) {
                output::info("{} script completed successfully", script_type);
                return true;
            } else {
                output::error("{} script failed with exit code: {}", script_type, exit_code);
                return false;
            }
            
        } catch (const std::exception& e) {
            output::error("Failed to run {} script: {}", script_type, e.what());
            return false;
        }
    }
    
    bool save_installation_info(const Manifest& manifest, 
                              const std::filesystem::path& app_dir) {
        try {
            InstallationInfo info;
            info.app_name = manifest.name;
            info.version = manifest.version;
            info.bucket = manifest.bucket;
            info.install_path = app_dir;
            info.install_time = std::chrono::system_clock::now();
            
            // Save to JSON file (use scoop-compatible filename)
            auto info_file = app_dir / "install.json";
            std::ofstream file(info_file);
            if (!file.is_open()) {
                return false;
            }
            
            // Create scoop-compatible install.json format
            nlohmann::json json;
            json["bucket"] = info.bucket;
            json["architecture"] = options_.architecture;
            // Optional: add url if available from manifest
            // json["url"] = manifest.url;
            
            file << json.dump(2);
            file.close();
            
            return true;
            
        } catch (const std::exception& e) {
            output::error("Failed to save installation info: {}", e.what());
            return false;
        }
    }
    
    bool ensure_7zip_available() {
        // Check if 7zip is already available
        if (Extractor::is_7zip_available()) {
            output::debug("7zip is already available");
            return true;
        }

        // Check if 7zip is installed via Scoop but not in PATH
        if (DependencyResolver::is_app_installed("7zip")) {
            output::debug("7zip is installed via Scoop");
            return true;
        }

        output::info("7zip not found, installing it first...");

        // Install 7zip first (without dependencies to avoid recursion)
        InstallOptions seven_zip_options = options_;
        seven_zip_options.skip_dependencies = true;

        // Temporarily create a new manager to avoid recursion
        InstallManager seven_zip_manager(seven_zip_options);
        auto seven_zip_result = seven_zip_manager.install_single_app("7zip");

        if (!seven_zip_result) {
            output::error("Failed to install 7zip");
            return false;
        }

        output::info("7zip installed successfully");
        return true;
    }

    template<typename Container>
    std::string join_strings(const Container& container, const std::string& delimiter) {
        if (container.empty()) return "";

        std::ostringstream oss;
        auto it = container.begin();
        oss << *it;
        ++it;

        for (; it != container.end(); ++it) {
            oss << delimiter << *it;
        }

        return oss.str();
    }

    void create_shortcuts(const Manifest& manifest, const std::filesystem::path& app_dir) {
        auto shortcuts = manifest.get_shortcuts();
        if (shortcuts.empty()) return;

        for (const auto& shortcut : shortcuts) {
            // Extract shortcut info - format: ["executable.exe", "Shortcut Name"]
            if (shortcut.size() >= 2) {
                std::string executable = shortcut[0];
                std::string shortcut_name = shortcut[1];

                std::cout << "Creating shortcut for " << shortcut_name << " (" << executable << ")\n";

                // TODO: Implement actual shortcut creation
                // For now, just log the action
            }
        }
    }

    void handle_persist_directories(const Manifest& manifest, const std::filesystem::path& app_dir) {
        auto persist_dirs = manifest.get_persist();
        if (persist_dirs.empty()) return;

        for (const auto& persist_dir : persist_dirs) {
            std::cout << "Persisting " << persist_dir << "\n";

            // TODO: Implement actual persist directory handling
            // This involves creating symlinks from app directory to persist directory
        }
    }

    void show_app_notes(const Manifest& manifest,
                       const std::filesystem::path& current_dir,
                       const std::filesystem::path& original_dir) {
        auto notes = manifest.get_notes();
        if (notes.empty()) return;

        std::cout << "\nNotes\n";
        std::cout << "-----\n";

        // Substitute variables like Scoop does
        std::string processed_notes = notes;

        // Replace $dir with current directory
        size_t pos = 0;
        while ((pos = processed_notes.find("$dir", pos)) != std::string::npos) {
            processed_notes.replace(pos, 4, current_dir.string());
            pos += current_dir.string().length();
        }

        // Replace $original_dir with original directory
        pos = 0;
        while ((pos = processed_notes.find("$original_dir", pos)) != std::string::npos) {
            processed_notes.replace(pos, 13, original_dir.string());
            pos += original_dir.string().length();
        }

        std::cout << processed_notes << "\n";
    }

    void run_installer(const Manifest& manifest, const std::filesystem::path& version_dir, bool uninstall) {
        // Scoop's Invoke-Installer function
        // TODO: Add get_installer/get_uninstaller methods to Manifest class
        // For now, skip installer execution
        /*
        auto installer = uninstall ? manifest.get_uninstaller() : manifest.get_installer();
        if (installer.empty()) return;

        std::cout << "Running " << (uninstall ? "uninstaller" : "installer") << "...\n";

        // TODO: Implement installer execution
        // This would involve running MSI installers, NSIS installers, etc.
        */
    }

    void ensure_install_dir_not_in_path(const std::filesystem::path& install_dir) {
        // Scoop's ensure_install_dir_not_in_path function
        // TODO: Check if install directory is in PATH and warn if it is
    }

    void create_startmenu_shortcuts(const Manifest& manifest, const std::filesystem::path& app_dir) {
        // Scoop's create_startmenu_shortcuts function
        auto shortcuts = manifest.get_shortcuts();
        if (shortcuts.empty()) return;

        for (const auto& shortcut : shortcuts) {
            if (shortcut.size() >= 2) {
                std::string executable = shortcut[0];
                std::string shortcut_name = shortcut[1];
                std::cout << "Creating shortcut for " << shortcut_name << " (" << executable << ")\n";
                // TODO: Implement actual shortcut creation
            }
        }
    }

    void install_psmodule(const Manifest& manifest, const std::filesystem::path& app_dir) {
        // Scoop's install_psmodule function
        // TODO: Add get_psmodule method to Manifest class
        // For now, skip PowerShell module installation
        /*
        auto psmodule = manifest.get_psmodule();
        if (psmodule.empty()) return;

        std::cout << "Installing PowerShell module...\n";
        // TODO: Implement PowerShell module installation
        */
    }

    void env_add_path(const Manifest& manifest, const std::filesystem::path& app_dir) {
        // Scoop's env_add_path function
        auto env_add_path = manifest.get_env_add_path();
        if (env_add_path.empty()) return;

        std::cout << "Adding to PATH environment variable...\n";
        // TODO: Implement PATH environment variable modification
    }

    void env_set(const Manifest& manifest) {
        // Scoop's env_set function
        auto env_set = manifest.get_env_set();
        if (env_set.empty()) return;

        std::cout << "Setting environment variables...\n";
        // TODO: Implement environment variable setting
    }

    void persist_permission(const Manifest& manifest) {
        // Scoop's persist_permission function
        // TODO: Implement persist permission handling
    }
};

} // namespace sco
