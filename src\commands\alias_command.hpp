#pragma once

#include "base_command.hpp"
#include "../core/config.hpp"
#include "../utils/table_formatter.hpp"
#include <iostream>
#include <filesystem>
#include <fstream>
#include <string>
#include <map>
#include <vector>
#include <algorithm>
#include <nlohmann/json.hpp>
#include "../utils/output.hpp"

namespace sco {

class AliasCommand : public BaseCommand {
public:
    AliasCommand() = default;
    
    int execute() override {
        try {
            if (action_.empty() || action_ == "list") {
                return list_aliases();
            } else if (action_ == "add") {
                return add_alias();
            } else if (action_ == "remove" || action_ == "rm") {
                return remove_alias();
            } else {
                output::error("'{}' is not one of available subcommands: add, rm, list", action_);
                show_usage();
                return 1;
            }
        } catch (const std::exception& e) {
            output::error("<PERSON>as command failed: {}", e.what());
            return 1;
        }
    }
    
    std::string get_name() const override { return "alias"; }
    std::string get_description() const override { return "Manage scoop aliases"; }
    
    void set_action(const std::string& action) { action_ = action; }
    void set_alias_name(const std::string& name) { alias_name_ = name; }
    void set_command(const std::string& command) { command_ = command; }
    
private:
    std::string action_;
    std::string alias_name_;
    std::string command_;
    
    void show_usage() {
        std::cout << "Usage: sco alias <subcommand> [options] [<args>]\n";
        std::cout << "Available subcommands: add, rm, list.\n\n";
        std::cout << "Aliases are custom Scoop subcommands that can be created to make common tasks easier.\n\n";
        std::cout << "To add an alias:\n";
        std::cout << "    sco alias add <n> <command> [<description>]\n\n";
        std::cout << "To remove an alias:\n";
        std::cout << "    sco alias rm <n>\n\n";
        std::cout << "To list all aliases:\n";
        std::cout << "    sco alias list [-v|--verbose]\n\n";
        std::cout << "Options:\n";
        std::cout << "  -v, --verbose  Show alias description and table headers (works only for \"list\")\n";
    }
    
    int list_aliases() {
        auto aliases = load_aliases();
        
        if (aliases.empty()) {
            output::info("No alias found.");
            return 0;
        }
        
        // Sort aliases by name
        std::vector<std::pair<std::string, std::string>> sorted_aliases(aliases.begin(), aliases.end());
        std::sort(sorted_aliases.begin(), sorted_aliases.end());
        
        // Display in table format similar to scoop
        for (const auto& alias : sorted_aliases) {
            std::cout << alias.first << " -> " << alias.second << "\n";
        }
        
        return 0;
    }
    
    int add_alias() {
        if (alias_name_.empty() || command_.empty()) {
            output::error("<n> and <command> must be specified for subcommand 'add'");
            return 1;
        }
        
        auto aliases = load_aliases();
        
        // Check if alias already exists
        if (aliases.find(alias_name_) != aliases.end()) {
            output::error("Alias '{}' already exists.", alias_name_);
            return 1;
        }
        
        // Add the alias
        aliases[alias_name_] = command_;
        
        if (save_aliases(aliases)) {
            return 0;
        } else {
            output::error("Failed to save alias.");
            return 1;
        }
    }
    
    int remove_alias() {
        if (alias_name_.empty()) {
            output::error("<n> must be specified for subcommand 'rm'");
            return 1;
        }
        
        auto aliases = load_aliases();
        
        auto it = aliases.find(alias_name_);
        if (it == aliases.end()) {
            output::error("Alias '{}' doesn't exist.", alias_name_);
            return 1;
        }
        
        output::info("Removing alias '{}'...", alias_name_);
        aliases.erase(it);
        
        if (save_aliases(aliases)) {
            return 0;
        } else {
            output::error("Failed to save changes.");
            return 1;
        }
    }
    
    std::map<std::string, std::string> load_aliases() {
        std::map<std::string, std::string> aliases;

        auto& config = Config::instance();
        auto aliases_file = config.get_scoop_dir() / "aliases.json";

        if (!std::filesystem::exists(aliases_file)) {
            return aliases;
        }

        try {
            std::ifstream file(aliases_file);
            nlohmann::json json;
            file >> json;

            if (json.is_object()) {
                for (auto& [key, value] : json.items()) {
                    if (value.is_string()) {
                        aliases[key] = value.get<std::string>();
                    }
                }
            }

        } catch (const std::exception& e) {
            output::error("Failed to load aliases: {}", e.what());
        }

        return aliases;
    }
    
    bool save_aliases(const std::map<std::string, std::string>& aliases) {
        auto& config = Config::instance();
        auto aliases_file = config.get_scoop_dir() / "aliases.json";

        try {
            // Create directory if it doesn't exist
            std::filesystem::create_directories(aliases_file.parent_path());

            nlohmann::json json;
            for (const auto& alias : aliases) {
                json[alias.first] = alias.second;
            }

            std::ofstream file(aliases_file);
            if (!file.is_open()) {
                return false;
            }

            file << json.dump(2);
            file.close();

            return true;

        } catch (const std::exception& e) {
            output::error("Failed to save aliases: {}", e.what());
            return false;
        }
    }
};

} // namespace sco
